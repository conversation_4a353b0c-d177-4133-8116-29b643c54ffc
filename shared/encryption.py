"""
加密模块

提供AES加密/解密功能和密码哈希功能。
使用cryptography库的Fernet进行对称加密，使用hashlib进行SHA256哈希。
"""

import hashlib
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class EncryptionHandler:
    """加密处理器类"""
    
    # 预共享密钥（实际项目中应该使用更安全的密钥管理方式）
    SHARED_SECRET = b"chatroom_secret_key_2024"
    SALT = b"chatroom_salt_2024_fixed"
    
    def __init__(self):
        """初始化加密处理器"""
        self.fernet = self._generate_fernet()
    
    def _generate_fernet(self) -> Fernet:
        """
        生成Fernet加密器
        
        Returns:
            Fernet: 加密器实例
        """
        # 使用PBKDF2从共享密钥生成Fernet密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.SALT,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.SHARED_SECRET))
        return Fernet(key)
    
    def encrypt(self, plaintext: str) -> bytes:
        """
        加密字符串
        
        Args:
            plaintext: 要加密的明文字符串
            
        Returns:
            bytes: 加密后的字节数据
        """
        try:
            # 将字符串转换为字节
            plaintext_bytes = plaintext.encode('utf-8')
            # 使用Fernet加密
            encrypted_data = self.fernet.encrypt(plaintext_bytes)
            return encrypted_data
        except Exception as e:
            raise ValueError(f"加密失败: {e}")
    
    def decrypt(self, ciphertext: bytes) -> str:
        """
        解密字节数据
        
        Args:
            ciphertext: 要解密的密文字节数据
            
        Returns:
            str: 解密后的明文字符串
        """
        try:
            # 使用Fernet解密
            decrypted_bytes = self.fernet.decrypt(ciphertext)
            # 将字节转换为字符串
            plaintext = decrypted_bytes.decode('utf-8')
            return plaintext
        except Exception as e:
            raise ValueError(f"解密失败: {e}")
    
    @staticmethod
    def hash_password(password: str) -> str:
        """
        对密码进行SHA256哈希
        
        Args:
            password: 原始密码
            
        Returns:
            str: 哈希后的密码（十六进制字符串）
        """
        # 使用SHA256哈希密码
        password_bytes = password.encode('utf-8')
        hash_object = hashlib.sha256(password_bytes)
        return hash_object.hexdigest()
    
    @staticmethod
    def verify_password(password: str, password_hash: str) -> bool:
        """
        验证密码
        
        Args:
            password: 原始密码
            password_hash: 存储的密码哈希
            
        Returns:
            bool: 密码是否匹配
        """
        return EncryptionHandler.hash_password(password) == password_hash


# 创建全局加密处理器实例
encryption_handler = EncryptionHandler()


def get_encryption_handler() -> EncryptionHandler:
    """
    获取全局加密处理器实例
    
    Returns:
        EncryptionHandler: 加密处理器实例
    """
    return encryption_handler
