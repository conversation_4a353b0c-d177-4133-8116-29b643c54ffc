"""
聊天室服务器主程序

实现多线程TCP服务器，处理客户端连接、用户认证、消息广播等功能。
支持并发连接，使用自定义协议进行通信。
"""

import socket
import threading
import time
from datetime import datetime
from typing import Dict

from shared import Protocol, MessageType, get_encryption_handler, get_message_size
from .database import DatabaseManager


class ChatServer:
    """聊天室服务器类"""

    def __init__(self, host: str = "localhost", port: int = 8888):
        """初始化服务器"""
        self.host = host
        self.port = port
        self.socket = None
        self.running = False

        # 客户端管理
        self.clients: Dict[socket.socket, str] = {}  # socket -> username
        self.client_heartbeats: Dict[socket.socket, float] = (
            {}
        )  # socket -> last_heartbeat_time
        self.client_lock = threading.Lock()

        # 心跳检查线程
        self.heartbeat_interval = 30  # 30秒心跳间隔
        self.heartbeat_timeout = 60  # 60秒超时
        self.heartbeat_thread = None

        # 初始化组件
        self.encryption_handler = get_encryption_handler()
        self.protocol = Protocol(self.encryption_handler)
        self.db_manager = DatabaseManager()

        print(f"服务器初始化完成 - {host}:{port}")

    def start(self):
        """启动服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind((self.host, self.port))
            self.socket.listen(10)

            self.running = True
            print(f"服务器启动成功，监听 {self.host}:{self.port}")
            print("等待客户端连接...")

            # 启动心跳检查线程
            self.heartbeat_thread = threading.Thread(
                target=self.heartbeat_checker, daemon=True
            )
            self.heartbeat_thread.start()

            while self.running:
                try:
                    client_socket, client_address = self.socket.accept()
                    print(f"新客户端连接: {client_address}")

                    # 为每个客户端创建处理线程
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, client_address),
                        daemon=True,
                    )
                    client_thread.start()

                except socket.error as e:
                    if self.running:
                        print(f"接受连接时出错: {e}")

        except Exception as e:
            print(f"服务器启动失败: {e}")
        finally:
            self.stop()

    def stop(self):
        """停止服务器"""
        self.running = False
        if self.socket:
            self.socket.close()

        # 关闭所有客户端连接
        with self.client_lock:
            for client_socket in list(self.clients.keys()):
                client_socket.close()
            self.clients.clear()

        print("服务器已停止")

    def heartbeat_checker(self):
        """心跳检查线程"""
        while self.running:
            current_time = time.time()
            disconnected_clients = []

            with self.client_lock:
                for client_socket, last_heartbeat in self.client_heartbeats.items():
                    if current_time - last_heartbeat > self.heartbeat_timeout:
                        disconnected_clients.append(client_socket)
                        print(
                            f"客户端心跳超时，断开连接: {self.clients.get(client_socket, 'Unknown')}"
                        )

                # 清理超时的客户端
                for client_socket in disconnected_clients:
                    if client_socket in self.clients:
                        username = self.clients[client_socket]
                        del self.clients[client_socket]
                        del self.client_heartbeats[client_socket]
                        client_socket.close()

                        # 广播用户列表更新
                        self.broadcast_user_list_update()

            time.sleep(self.heartbeat_interval)

    def handle_client(self, client_socket: socket.socket, client_address):
        """处理单个客户端连接"""
        username = None
        authenticated = False

        try:
            while self.running:
                # 接收消息
                message_data = self.receive_message(client_socket)
                if not message_data:
                    break

                # 解析消息
                parsed_message = self.protocol.unpack_message(message_data)
                if not parsed_message:
                    continue

                msg_type, payload, _ = parsed_message  # 忽略seq_num

                # 处理不同类型的消息
                if msg_type == MessageType.REGISTER_REQUEST:
                    self.handle_register(client_socket, payload)

                elif msg_type == MessageType.LOGIN_REQUEST:
                    success, username = self.handle_login(client_socket, payload)
                    if success:
                        authenticated = True
                        with self.client_lock:
                            self.clients[client_socket] = username
                            self.client_heartbeats[client_socket] = time.time()

                        # 广播用户列表更新
                        self.broadcast_user_list_update()

                elif msg_type == MessageType.CHAT_MESSAGE and authenticated:
                    self.handle_chat_message(client_socket, payload, username)

                elif msg_type == MessageType.HISTORY_REQUEST and authenticated:
                    self.handle_history_request(client_socket, payload)

                elif msg_type == MessageType.USER_LIST_REQUEST and authenticated:
                    self.handle_user_list_request(client_socket)

                elif msg_type == MessageType.HEARTBEAT:
                    # 更新心跳时间
                    with self.client_lock:
                        self.client_heartbeats[client_socket] = time.time()

                else:
                    # 未认证用户尝试发送需要认证的消息
                    if not authenticated:
                        error_msg = self.protocol.create_error_notification(
                            "AUTH_REQUIRED", "请先登录"
                        )
                        self.send_message(client_socket, error_msg)

        except Exception as e:
            print(f"处理客户端 {client_address} 时出错: {e}")
        finally:
            # 清理客户端连接
            with self.client_lock:
                if client_socket in self.clients:
                    username = self.clients[client_socket]
                    del self.clients[client_socket]
                    if client_socket in self.client_heartbeats:
                        del self.client_heartbeats[client_socket]
                    print(f"用户 {username} 断开连接")

                    # 广播用户列表更新
                    self.broadcast_user_list_update()

            client_socket.close()

    def receive_message(self, client_socket: socket.socket) -> bytes:
        """接收完整的消息"""
        try:
            # 先接收Header
            header_data = b""
            while len(header_data) < Protocol.HEADER_SIZE:
                chunk = client_socket.recv(Protocol.HEADER_SIZE - len(header_data))
                if not chunk:
                    return None
                header_data += chunk

            # 从Header中获取消息总长度
            total_size = get_message_size(header_data)
            if not total_size:
                return None

            # 接收完整消息
            message_data = header_data
            while len(message_data) < total_size:
                chunk = client_socket.recv(total_size - len(message_data))
                if not chunk:
                    return None
                message_data += chunk

            return message_data

        except socket.error:
            return None

    def send_message(self, client_socket: socket.socket, message: bytes) -> bool:
        """发送消息到客户端"""
        try:
            client_socket.sendall(message)
            return True
        except socket.error:
            return False

    def handle_register(self, client_socket: socket.socket, payload: dict):
        """处理用户注册请求"""
        username = payload.get("username")
        password_hash = payload.get("password_hash")

        if not username or not password_hash:
            response = self.protocol.create_register_response(
                False, "用户名和密码不能为空"
            )
        else:
            success, message = self.db_manager.register_user(username, password_hash)
            response = self.protocol.create_register_response(success, message)

        self.send_message(client_socket, response)

    def handle_login(self, client_socket: socket.socket, payload: dict) -> tuple:
        """处理用户登录请求"""
        username = payload.get("username")
        password_hash = payload.get("password_hash")

        if not username or not password_hash:
            response = self.protocol.create_login_response(
                False, "用户名和密码不能为空"
            )
            self.send_message(client_socket, response)
            return False, None

        success, message = self.db_manager.verify_user(username, password_hash)

        if success:
            response = self.protocol.create_login_response(
                True, f"欢迎 {username}！", username
            )
            self.send_message(client_socket, response)
            return True, username
        else:
            response = self.protocol.create_login_response(False, message)
            self.send_message(client_socket, response)
            return False, None

    def handle_chat_message(
        self, client_socket: socket.socket, payload: dict, username: str
    ):
        """处理聊天消息"""
        message = payload.get("message")
        if not message:
            return

        # 添加时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 存储消息到数据库
        encrypted_payload = self.encryption_handler.encrypt(message)
        self.db_manager.store_message(username, encrypted_payload)

        # 广播消息给所有在线用户
        broadcast_msg = self.protocol.create_broadcast_message(
            username, message, timestamp
        )
        self.broadcast_message(broadcast_msg, exclude_socket=client_socket)

    def handle_history_request(self, client_socket: socket.socket, payload: dict):
        """处理历史消息请求"""
        count = payload.get("count", 10)

        # 从数据库获取历史消息
        db_messages = self.db_manager.get_recent_messages(count)

        # 解密消息并格式化
        messages = []
        for db_msg in db_messages:
            try:
                decrypted_message = self.encryption_handler.decrypt(
                    db_msg["encrypted_payload"]
                )
                messages.append(
                    {
                        "username": db_msg["sender_username"],
                        "message": decrypted_message,
                        "timestamp": db_msg["timestamp"],
                    }
                )
            except Exception as e:
                print(f"解密历史消息失败: {e}")

        # 发送历史消息响应
        response = self.protocol.create_history_response(messages)
        self.send_message(client_socket, response)

    def handle_user_list_request(self, client_socket: socket.socket):
        """处理用户列表请求"""
        with self.client_lock:
            online_users = list(self.clients.values())

        response = self.protocol.create_user_list_update(online_users)
        self.send_message(client_socket, response)

    def broadcast_user_list_update(self):
        """广播用户列表更新给所有在线用户"""
        with self.client_lock:
            online_users = list(self.clients.values())
            if not online_users:
                return

            message = self.protocol.create_user_list_update(online_users)
            disconnected_clients = []

            for client_socket in self.clients:
                if not self.send_message(client_socket, message):
                    disconnected_clients.append(client_socket)

            # 清理断开的连接
            for client_socket in disconnected_clients:
                if client_socket in self.clients:
                    username = self.clients[client_socket]
                    del self.clients[client_socket]
                    if client_socket in self.client_heartbeats:
                        del self.client_heartbeats[client_socket]
                    print(f"清理断开的连接: {username}")

    def broadcast_message(self, message: bytes, exclude_socket: socket.socket = None):
        """广播消息给所有在线客户端"""
        with self.client_lock:
            disconnected_clients = []

            for client_socket in self.clients:
                if client_socket != exclude_socket:
                    if not self.send_message(client_socket, message):
                        disconnected_clients.append(client_socket)

            # 清理断开的连接
            for client_socket in disconnected_clients:
                if client_socket in self.clients:
                    username = self.clients[client_socket]
                    del self.clients[client_socket]
                    print(f"清理断开的连接: {username}")


def main():
    """主函数"""
    server = ChatServer()

    try:
        server.start()
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭服务器...")
        server.stop()


if __name__ == "__main__":
    main()
