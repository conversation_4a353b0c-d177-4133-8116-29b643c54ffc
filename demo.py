#!/usr/bin/env python3
"""
聊天室系统演示脚本

展示优化后的聊天室客户端界面和功能。
"""

import os
import sys
import time
import subprocess
import signal
from pathlib import Path

def print_colored(text, color_code):
    """打印彩色文本"""
    print(f"\033[{color_code}m{text}\033[0m")

def print_banner():
    """打印演示横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🎯 聊天室系统演示 🎯                        ║
║                   基于Python Socket实现                      ║
║                     优化版命令行界面                          ║
╚══════════════════════════════════════════════════════════════╝
"""
    print_colored(banner, "96")  # 亮青色

def print_section(title):
    """打印章节标题"""
    print_colored(f"\n{'='*60}", "90")
    print_colored(f"  {title}", "93")
    print_colored(f"{'='*60}", "90")

def check_environment():
    """检查运行环境"""
    print_section("🔍 检查运行环境")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version >= (3, 8):
        print_colored(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}", "92")
    else:
        print_colored(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}", "91")
        return False
    
    # 检查文件结构
    required_files = [
        "shared/protocol.py",
        "shared/encryption.py", 
        "server/server.py",
        "server/database.py",
        "client/client.py",
        "requirements.txt"
    ]
    
    for file_path in required_files:
        if Path(file_path).exists():
            print_colored(f"✅ {file_path}", "92")
        else:
            print_colored(f"❌ 缺少文件: {file_path}", "91")
            return False
    
    # 检查依赖
    try:
        import cryptography
        print_colored(f"✅ cryptography库已安装", "92")
    except ImportError:
        print_colored(f"❌ 缺少cryptography库", "91")
        print_colored("请运行: conda install cryptography", "93")
        return False
    
    return True

def show_features():
    """展示功能特性"""
    print_section("🚀 功能特性")
    
    features = [
        ("🎨 美观界面", "彩色命令行界面，支持emoji和Unicode字符"),
        ("🔐 安全通信", "AES加密消息传输，SHA256密码哈希"),
        ("👥 多用户支持", "支持多个用户同时在线聊天"),
        ("📜 历史记录", "SQLite数据库存储，支持历史消息查询"),
        ("⚡ 实时通信", "基于TCP Socket的实时消息传输"),
        ("🛠️ 自定义协议", "Header-Payload结构，网络字节序"),
        ("🧵 并发处理", "多线程处理，支持并发连接"),
        ("📊 状态监控", "连接状态、用户统计等信息显示"),
    ]
    
    for feature, description in features:
        print_colored(f"  {feature:<15} - {description}", "97")

def show_commands():
    """展示命令说明"""
    print_section("📋 命令说明")
    
    commands = [
        ("📝 /register <用户名> <密码>", "注册新用户账户"),
        ("🔑 /login <用户名> <密码>", "登录已有账户"),
        ("💬 直接输入文本", "发送聊天消息（需要先登录）"),
        ("📜 /history [数量]", "查看历史消息（默认10条）"),
        ("📊 /status", "显示连接和用户状态"),
        ("❓ /help", "显示帮助信息"),
        ("🧹 /clear", "清屏并重新显示横幅"),
        ("🚪 /quit", "退出程序"),
    ]
    
    for cmd, desc in commands:
        print_colored(f"  {cmd:<30} - {desc}", "97")

def show_usage_example():
    """展示使用示例"""
    print_section("💡 使用示例")
    
    example_steps = [
        "1. 启动服务器：cd server && python server.py",
        "2. 启动客户端：cd client && python client.py",
        "3. 注册用户：/register alice password123",
        "4. 登录账户：/login alice password123", 
        "5. 发送消息：Hello everyone!",
        "6. 查看历史：/history 5",
        "7. 查看状态：/status",
        "8. 退出程序：/quit"
    ]
    
    for step in example_steps:
        print_colored(f"  {step}", "97")
        time.sleep(0.3)

def show_architecture():
    """展示系统架构"""
    print_section("🏗️ 系统架构")
    
    architecture = """
    ┌─────────────────┐    TCP Socket    ┌─────────────────┐
    │   客户端 A      │◄─────────────────►│                 │
    └─────────────────┘                  │                 │
    ┌─────────────────┐    自定义协议     │     服务器      │
    │   客户端 B      │◄─────────────────►│                 │
    └─────────────────┘                  │                 │
    ┌─────────────────┐    AES加密       │                 │
    │   客户端 C      │◄─────────────────►│                 │
    └─────────────────┘                  └─────────────────┘
                                                   │
                                                   ▼
                                         ┌─────────────────┐
                                         │  SQLite数据库   │
                                         │  用户信息       │
                                         │  消息历史       │
                                         └─────────────────┘
    """
    
    print_colored(architecture, "96")

def interactive_demo():
    """交互式演示"""
    print_section("🎮 交互式演示")
    
    print_colored("是否要启动交互式演示？(y/n): ", "93")
    choice = input().strip().lower()
    
    if choice == 'y':
        print_colored("\n正在启动服务器...", "93")
        print_colored("请在新终端中运行以下命令：", "97")
        print_colored("cd server && python server.py", "92")
        
        input("\n按回车键继续...")
        
        print_colored("\n正在启动客户端...", "93")
        print_colored("请在另一个新终端中运行以下命令：", "97")
        print_colored("cd client && python client.py", "92")
        
        print_colored("\n现在您可以体验美观的聊天室界面了！", "96")
    else:
        print_colored("演示结束。", "93")

def main():
    """主函数"""
    # 清屏
    os.system('cls' if os.name == 'nt' else 'clear')
    
    # 显示横幅
    print_banner()
    
    # 检查环境
    if not check_environment():
        print_colored("\n❌ 环境检查失败，请修复上述问题后重试。", "91")
        return
    
    # 展示功能
    show_features()
    
    # 展示命令
    show_commands()
    
    # 展示架构
    show_architecture()
    
    # 展示使用示例
    show_usage_example()
    
    # 交互式演示
    interactive_demo()
    
    print_colored("\n🎉 感谢使用聊天室系统演示！", "96")

if __name__ == "__main__":
    main()
